@using ShiningCMusicApp.Services.Email
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons

<SfDialog @ref="dialogRef" @bind-Visible="IsVisible" Header="Email Results" Width="900px" Height="600px" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <div class="container-fluid">
                <!-- Summary Stats -->
                <div class="row mb-3">
                    <div class="col-md-4 text-center">
                        <div class="card border-success">
                            <div class="card-body">
                                <h3 class="text-success">@(Results?.SuccessCount ?? 0)</h3>
                                <small>Successfully Sent</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="card border-danger">
                            <div class="card-body">
                                <h3 class="text-danger">@(Results?.ErrorCount ?? 0)</h3>
                                <small>Failed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="card border-info">
                            <div class="card-body">
                                <h3 class="text-info">@(Results?.TotalAttempted ?? 0)</h3>
                                <small>Total Attempted</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Results -->
                <div class="row">
                    <div class="col-12">
                        <h6>Detailed Results</h6>
                        @if (Results?.Results != null && Results.Results.Any())
                        {
                            <SfGrid DataSource="@Results.Results" AllowPaging="true" Height="300px">
                                <GridPageSettings PageSize="10"></GridPageSettings>
                                <GridColumns>
                                    <GridColumn Field="@nameof(EmailResult.RecipientName)" HeaderText="Recipient" Width="150"></GridColumn>
                                    <GridColumn Field="@nameof(EmailResult.Email)" HeaderText="Email" Width="200"></GridColumn>
                                    <GridColumn Field="@nameof(EmailResult.Success)" HeaderText="Status" Width="100">
                                        <Template>
                                            @{
                                                var result = context as EmailResult;
                                            }
                                            @if (result?.Success == true)
                                            {
                                                <span class="badge bg-success">Success</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Failed</span>
                                            }
                                        </Template>
                                    </GridColumn>
                                    <GridColumn Field="@nameof(EmailResult.ErrorMessage)" HeaderText="Error" Width="250"></GridColumn>
                                    <GridColumn Field="@nameof(EmailResult.SentAt)" HeaderText="Time" Width="150" Format="HH:mm:ss"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-inbox"></i>
                                <p>No results to display</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </Content>
        <FooterTemplate>
            <SfButton CssClass="btn e-success" @onclick="CloseModal">
                    <i class="bi bi-check me-2"></i>Close
            </SfButton>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>


