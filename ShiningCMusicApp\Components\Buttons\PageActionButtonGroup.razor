<div class="@ContainerClass">
    @if (ShowFirstButton)
    {
        <button class="@GetFirstButtonClass()"
                style="@FirstButtonStyle"
                @onclick="OnFirstClick"
                disabled="@IsFirstDisabled">
            @if (FirstIconContent != null)
            {
                @FirstIconContent
            }
            else if (!string.IsNullOrEmpty(FirstIcon))
            {
                <i class="@FirstIcon" style="color: @FirstIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(FirstTextDesktop))
            {
                <span class="d-none d-sm-inline @((!string.IsNullOrEmpty(FirstIcon) || FirstIconContent != null) ? "ms-2" : "")">@FirstTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(FirstTextMobile))
            {
                <span class="d-sm-none @((!string.IsNullOrEmpty(FirstIcon) || FirstIconContent != null) ? "ms-2" : "")">@FirstTextMobile</span>
            }
            @if (string.IsNullOrEmpty(FirstTextDesktop) && string.IsNullOrEmpty(FirstTextMobile) && !string.IsNullOrEmpty(FirstText))
            {
                <span class="@((!string.IsNullOrEmpty(FirstIcon) || FirstIconContent != null) ? "ms-2" : "")">@FirstText</span>
            }
        </button>
    }

    @if (ShowSecondButton)
    {
        <button class="@GetSecondButtonClass()"
                style="@SecondButtonStyle"
                @onclick="OnSecondClick"
                disabled="@IsSecondDisabled">
            @if (!string.IsNullOrEmpty(SecondIcon))
            {
                <i class="@SecondIcon" style="color: @SecondIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(SecondTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(SecondIcon) ? "ms-2" : "")">@SecondTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(SecondTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(SecondIcon) ? "ms-2" : "")">@SecondTextMobile</span>
            }
            @if (string.IsNullOrEmpty(SecondTextDesktop) && string.IsNullOrEmpty(SecondTextMobile) && !string.IsNullOrEmpty(SecondText))
            {
                <span class="@(!string.IsNullOrEmpty(SecondIcon) ? "ms-2" : "")">@SecondText</span>
            }
        </button>
    }

    @if (ShowThirdButton)
    {
        <button class="@GetThirdButtonClass()"
                style="@ThirdButtonStyle"
                @onclick="OnThirdClick"
                disabled="@IsThirdDisabled">
            @if (!string.IsNullOrEmpty(ThirdIcon))
            {
                <i class="@ThirdIcon" style="color: @ThirdIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(ThirdTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(ThirdIcon) ? "ms-2" : "")">@ThirdTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(ThirdTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(ThirdIcon) ? "ms-2" : "")">@ThirdTextMobile</span>
            }
            @if (string.IsNullOrEmpty(ThirdTextDesktop) && string.IsNullOrEmpty(ThirdTextMobile) && !string.IsNullOrEmpty(ThirdText))
            {
                <span class="@(!string.IsNullOrEmpty(ThirdIcon) ? "ms-2" : "")">@ThirdText</span>
            }
        </button>
    }

    @if (ShowFourthButton)
    {
        <button class="@GetFourthButtonClass()"
                style="@FourthButtonStyle"
                @onclick="OnFourthClick"
                disabled="@IsFourthDisabled">
            @if (!string.IsNullOrEmpty(FourthIcon))
            {
                <i class="@FourthIcon" style="color: @FourthIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(FourthTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(FourthIcon) ? "ms-2" : "")">@FourthTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(FourthTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(FourthIcon) ? "ms-2" : "")">@FourthTextMobile</span>
            }
            @if (string.IsNullOrEmpty(FourthTextDesktop) && string.IsNullOrEmpty(FourthTextMobile) && !string.IsNullOrEmpty(FourthText))
            {
                <span class="@(!string.IsNullOrEmpty(FourthIcon) ? "ms-2" : "")">@FourthText</span>
            }
        </button>
    }
</div>
